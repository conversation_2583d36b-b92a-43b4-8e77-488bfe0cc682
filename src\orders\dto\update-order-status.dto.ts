import { IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { OrderStatus } from '../entities/order.entity';

export class UpdateOrderStatusDto {
  @ApiProperty({
    enum: ['pending', 'processing', 'ready', 'delivered', 'cancelled'],
    description: 'New status for the order',
  })
  @IsEnum(['pending', 'processing', 'ready', 'delivered', 'cancelled'])
  status: OrderStatus;
}
