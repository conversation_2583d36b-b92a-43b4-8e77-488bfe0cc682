# STORE MICROSERVICE - POSTM<PERSON> EXAMPLES
# =====================================

## CONFIGURACIÓN INICIAL
Base URL: http://localhost:3001/api
Auth URL: http://localhost:3000/auth

## 1. AUTENTICACIÓN (Microservicio de Auth)
### Obtener Token JWT
POST {{auth_url}}/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### Respuesta Esperada:
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.signature",
  "user": {
    "id": "65789abcdef**********",
    "email": "<EMAIL>",
    "role": "store",
    "name": "<PERSON> Tienda",
    "phone": "+**********",
    "address": "Calle Principal 123",
    "storeData": {
      "storeName": "Tienda Ejemplo",
      "storeAddress": "Calle Principal 123",
      "storePhone": "+**********"
    }
  }
}

## 2. HEALTH CHECK
### Verificar Estado del Servicio
GET {{base_url}}/health

### Respuesta Esperada:
{
  "status": "ok",
  "timestamp": "2024-01-15T10:00:00.000Z",
  "service": "store-microservice"
}

## 3. DASHBOARD
### Obtener Estadísticas del Dashboard
GET {{base_url}}/store/dashboard
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
{
  "totalProducts": 25,
  "pendingOrders": 8,
  "completedOrders": 142,
  "totalRevenue": 15750.50,
  "averageRating": 4.3
}

## 4. PRODUCTOS
### 4.1 Crear Producto
POST {{base_url}}/store/products
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "name": "Pizza Margherita",
  "description": "Pizza clásica con tomate, mozzarella y albahaca fresca",
  "price": 12.99,
  "stock": 50,
  "category": "Pizzas",
  "image": "https://ejemplo.com/pizza-margherita.jpg",
  "tags": ["vegetariano", "clásico", "italiano"],
  "deliveryOptions": {
    "delivery": true,
    "pickup": true
  },
  "nutritionalInfo": {
    "calories": 280,
    "protein": 12,
    "carbs": 35,
    "fat": 8
  }
}

### Respuesta Esperada:
{
  "_id": "65789abcdef1234567891",
  "name": "Pizza Margherita",
  "description": "Pizza clásica con tomate, mozzarella y albahaca fresca",
  "price": 12.99,
  "stock": 50,
  "category": "Pizzas",
  "image": "https://ejemplo.com/pizza-margherita.jpg",
  "tags": ["vegetariano", "clásico", "italiano"],
  "deliveryOptions": {
    "delivery": true,
    "pickup": true
  },
  "nutritionalInfo": {
    "calories": 280,
    "protein": 12,
    "carbs": 35,
    "fat": 8
  },
  "storeId": "65789abcdef**********",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}

### 4.2 Obtener Todos los Productos
GET {{base_url}}/store/products
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
[
  {
    "_id": "65789abcdef1234567891",
    "name": "Pizza Margherita",
    "description": "Pizza clásica con tomate, mozzarella y albahaca fresca",
    "price": 12.99,
    "stock": 50,
    "category": "Pizzas",
    "image": "https://ejemplo.com/pizza-margherita.jpg",
    "tags": ["vegetariano", "clásico", "italiano"],
    "deliveryOptions": {
      "delivery": true,
      "pickup": true
    },
    "nutritionalInfo": {
      "calories": 280,
      "protein": 12,
      "carbs": 35,
      "fat": 8
    },
    "storeId": "65789abcdef**********",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  },
  {
    "_id": "65789abcdef1234567892",
    "name": "Pizza Pepperoni",
    "description": "Pizza con pepperoni y queso mozzarella",
    "price": 14.99,
    "stock": 30,
    "category": "Pizzas",
    "image": "https://ejemplo.com/pizza-pepperoni.jpg",
    "tags": ["clásico", "carne"],
    "deliveryOptions": {
      "delivery": true,
      "pickup": true
    },
    "storeId": "65789abcdef**********",
    "createdAt": "2024-01-15T09:15:00.000Z",
    "updatedAt": "2024-01-15T09:15:00.000Z"
  }
]

### 4.3 Obtener Producto Específico
GET {{base_url}}/store/products/65789abcdef1234567891
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
{
  "_id": "65789abcdef1234567891",
  "name": "Pizza Margherita",
  "description": "Pizza clásica con tomate, mozzarella y albahaca fresca",
  "price": 12.99,
  "stock": 50,
  "category": "Pizzas",
  "image": "https://ejemplo.com/pizza-margherita.jpg",
  "tags": ["vegetariano", "clásico", "italiano"],
  "deliveryOptions": {
    "delivery": true,
    "pickup": true
  },
  "nutritionalInfo": {
    "calories": 280,
    "protein": 12,
    "carbs": 35,
    "fat": 8
  },
  "storeId": "65789abcdef**********",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}

### 4.4 Actualizar Producto
PATCH {{base_url}}/store/products/65789abcdef1234567891
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "price": 13.99,
  "stock": 45,
  "tags": ["vegetariano", "clásico", "italiano", "popular"]
}

### Respuesta Esperada:
{
  "_id": "65789abcdef1234567891",
  "name": "Pizza Margherita",
  "description": "Pizza clásica con tomate, mozzarella y albahaca fresca",
  "price": 13.99,
  "stock": 45,
  "category": "Pizzas",
  "image": "https://ejemplo.com/pizza-margherita.jpg",
  "tags": ["vegetariano", "clásico", "italiano", "popular"],
  "deliveryOptions": {
    "delivery": true,
    "pickup": true
  },
  "nutritionalInfo": {
    "calories": 280,
    "protein": 12,
    "carbs": 35,
    "fat": 8
  },
  "storeId": "65789abcdef**********",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T11:45:00.000Z"
}

### 4.5 Eliminar Producto
DELETE {{base_url}}/store/products/65789abcdef1234567891
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
Status: 200 OK
(Sin contenido en el body)

## 5. PEDIDOS
### 5.1 Obtener Todos los Pedidos
GET {{base_url}}/store/orders
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
[
  {
    "_id": "65789abcdef1234567893",
    "customer": {
      "name": "Juan Pérez",
      "phone": "+**********",
      "email": "<EMAIL>",
      "address": "Av. Libertador 456"
    },
    "total": 25.98,
    "status": "pending",
    "deliveryMethod": "delivery",
    "items": [
      {
        "id": "65789abcdef1234567891",
        "name": "Pizza Margherita",
        "quantity": 2,
        "price": 12.99
      }
    ],
    "notes": "Sin cebolla, por favor",
    "storeId": "65789abcdef**********",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  },
  {
    "_id": "65789abcdef1234567894",
    "customer": {
      "name": "María González",
      "phone": "+1234567891",
      "email": "<EMAIL>"
    },
    "total": 14.99,
    "status": "ready",
    "deliveryMethod": "pickup",
    "items": [
      {
        "id": "65789abcdef1234567892",
        "name": "Pizza Pepperoni",
        "quantity": 1,
        "price": 14.99
      }
    ],
    "storeId": "65789abcdef**********",
    "createdAt": "2024-01-15T09:15:00.000Z",
    "updatedAt": "2024-01-15T11:20:00.000Z"
  }
]

### 5.2 Filtrar Pedidos por Estado
GET {{base_url}}/store/orders?status=pending
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
[
  {
    "_id": "65789abcdef1234567893",
    "customer": {
      "name": "Juan Pérez",
      "phone": "+**********",
      "email": "<EMAIL>",
      "address": "Av. Libertador 456"
    },
    "total": 25.98,
    "status": "pending",
    "deliveryMethod": "delivery",
    "items": [
      {
        "id": "65789abcdef1234567891",
        "name": "Pizza Margherita",
        "quantity": 2,
        "price": 12.99
      }
    ],
    "notes": "Sin cebolla, por favor",
    "storeId": "65789abcdef**********",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
]

### 5.3 Obtener Pedido Específico
GET {{base_url}}/store/orders/65789abcdef1234567893
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
{
  "_id": "65789abcdef1234567893",
  "customer": {
    "name": "Juan Pérez",
    "phone": "+**********",
    "email": "<EMAIL>",
    "address": "Av. Libertador 456"
  },
  "total": 25.98,
  "status": "pending",
  "deliveryMethod": "delivery",
  "items": [
    {
      "id": "65789abcdef1234567891",
      "name": "Pizza Margherita",
      "quantity": 2,
      "price": 12.99
    }
  ],
  "notes": "Sin cebolla, por favor",
  "storeId": "65789abcdef**********",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}

### 5.4 Actualizar Estado del Pedido
PATCH {{base_url}}/store/orders/65789abcdef1234567893/status
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "status": "processing"
}

### Respuesta Esperada:
{
  "_id": "65789abcdef1234567893",
  "customer": {
    "name": "Juan Pérez",
    "phone": "+**********",
    "email": "<EMAIL>",
    "address": "Av. Libertador 456"
  },
  "total": 25.98,
  "status": "processing",
  "deliveryMethod": "delivery",
  "items": [
    {
      "id": "65789abcdef1234567891",
      "name": "Pizza Margherita",
      "quantity": 2,
      "price": 12.99
    }
  ],
  "notes": "Sin cebolla, por favor",
  "storeId": "65789abcdef**********",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T11:50:00.000Z"
}

### Estados válidos: "pending", "processing", "ready", "delivered", "cancelled"

## 6. PROVEEDORES
### 6.1 Crear Proveedor
POST {{base_url}}/store/suppliers
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "name": "Distribuidora La Italiana",
  "contactPerson": "Marco Rossi",
  "email": "<EMAIL>",
  "phone": "+1234567891",
  "address": "Zona Industrial 789",
  "products": ["queso mozzarella", "tomate san marzano", "aceite de oliva"]
}

### Respuesta Esperada:
{
  "_id": "65789abcdef1234567895",
  "name": "Distribuidora La Italiana",
  "contactPerson": "Marco Rossi",
  "email": "<EMAIL>",
  "phone": "+1234567891",
  "address": "Zona Industrial 789",
  "products": ["queso mozzarella", "tomate san marzano", "aceite de oliva"],
  "storeId": "65789abcdef**********",
  "createdAt": "2024-01-15T12:00:00.000Z",
  "updatedAt": "2024-01-15T12:00:00.000Z"
}

### 6.2 Obtener Todos los Proveedores
GET {{base_url}}/store/suppliers
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
[
  {
    "_id": "65789abcdef1234567895",
    "name": "Distribuidora La Italiana",
    "contactPerson": "Marco Rossi",
    "email": "<EMAIL>",
    "phone": "+1234567891",
    "address": "Zona Industrial 789",
    "products": ["queso mozzarella", "tomate san marzano", "aceite de oliva"],
    "storeId": "65789abcdef**********",
    "createdAt": "2024-01-15T12:00:00.000Z",
    "updatedAt": "2024-01-15T12:00:00.000Z"
  },
  {
    "_id": "65789abcdef1234567896",
    "name": "Carnes Premium",
    "contactPerson": "Carlos Mendoza",
    "email": "<EMAIL>",
    "phone": "+1234567892",
    "address": "Mercado Central 456",
    "products": ["pepperoni", "jamón", "chorizo"],
    "storeId": "65789abcdef**********",
    "createdAt": "2024-01-14T08:30:00.000Z",
    "updatedAt": "2024-01-14T08:30:00.000Z"
  }
]

### 6.3 Obtener Proveedor Específico
GET {{base_url}}/store/suppliers/65789abcdef1234567895
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
{
  "_id": "65789abcdef1234567895",
  "name": "Distribuidora La Italiana",
  "contactPerson": "Marco Rossi",
  "email": "<EMAIL>",
  "phone": "+1234567891",
  "address": "Zona Industrial 789",
  "products": ["queso mozzarella", "tomate san marzano", "aceite de oliva"],
  "storeId": "65789abcdef**********",
  "createdAt": "2024-01-15T12:00:00.000Z",
  "updatedAt": "2024-01-15T12:00:00.000Z"
}

### 6.4 Actualizar Proveedor
PATCH {{base_url}}/store/suppliers/65789abcdef1234567895
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "phone": "+1234567893",
  "products": ["queso mozzarella", "tomate san marzano", "aceite de oliva", "albahaca fresca"]
}

### Respuesta Esperada:
{
  "_id": "65789abcdef1234567895",
  "name": "Distribuidora La Italiana",
  "contactPerson": "Marco Rossi",
  "email": "<EMAIL>",
  "phone": "+1234567893",
  "address": "Zona Industrial 789",
  "products": ["queso mozzarella", "tomate san marzano", "aceite de oliva", "albahaca fresca"],
  "storeId": "65789abcdef**********",
  "createdAt": "2024-01-15T12:00:00.000Z",
  "updatedAt": "2024-01-15T13:15:00.000Z"
}

### 6.5 Eliminar Proveedor
DELETE {{base_url}}/store/suppliers/65789abcdef1234567895
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
Status: 200 OK
(Sin contenido en el body)

## 7. REVIEWS
### 7.1 Obtener Todas las Reviews
GET {{base_url}}/store/reviews
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
{
  "reviews": [
    {
      "_id": "65789abcdef1234567897",
      "customer": "María González",
      "rating": 5,
      "comment": "¡Excelente pizza! Muy recomendada, la masa estaba perfecta y los ingredientes frescos.",
      "productId": "65789abcdef1234567891",
      "productName": "Pizza Margherita",
      "storeId": "65789abcdef**********",
      "createdAt": "2024-01-15T14:20:00.000Z",
      "updatedAt": "2024-01-15T14:20:00.000Z"
    },
    {
      "_id": "65789abcdef1234567898",
      "customer": "Carlos Ruiz",
      "rating": 4,
      "comment": "Muy buena pizza, llegó caliente y a tiempo. El servicio fue excelente.",
      "productId": "65789abcdef1234567892",
      "productName": "Pizza Pepperoni",
      "storeId": "65789abcdef**********",
      "createdAt": "2024-01-15T13:45:00.000Z",
      "updatedAt": "2024-01-15T13:45:00.000Z"
    },
    {
      "_id": "65789abcdef1234567899",
      "customer": "Ana López",
      "rating": 5,
      "comment": "Increíble experiencia, definitivamente volveré a pedir. La atención al cliente es de primera.",
      "storeId": "65789abcdef**********",
      "createdAt": "2024-01-14T19:30:00.000Z",
      "updatedAt": "2024-01-14T19:30:00.000Z"
    }
  ],
  "averageRating": 4.67,
  "totalReviews": 3
}

## 8. ERRORES COMUNES Y RESPUESTAS

### 8.1 Error de Autenticación (401)
### Request sin token o token inválido:
GET {{base_url}}/store/products

### Respuesta Esperada:
Status: 401 Unauthorized
{
  "statusCode": 401,
  "message": "Unauthorized"
}

### 8.2 Error de Permisos (403)
### Usuario con rol diferente a 'store':
GET {{base_url}}/store/products
Authorization: Bearer {{customer_token}}

### Respuesta Esperada:
Status: 403 Forbidden
{
  "statusCode": 403,
  "message": "Only store users can access products"
}

### 8.3 Recurso No Encontrado (404)
GET {{base_url}}/store/products/65789abcdef1234567999
Authorization: Bearer {{access_token}}

### Respuesta Esperada:
Status: 404 Not Found
{
  "statusCode": 404,
  "message": "Product not found"
}

### 8.4 Error de Validación (400)
POST {{base_url}}/store/products
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "name": "",
  "price": -5,
  "stock": "invalid"
}

### Respuesta Esperada:
Status: 400 Bad Request
{
  "statusCode": 400,
  "message": [
    "name should not be empty",
    "price must not be less than 0",
    "stock must be a number conforming to the specified constraints"
  ],
  "error": "Bad Request"
}

## 9. CONFIGURACIÓN DE POSTMAN

### Variables de Entorno:
- base_url: http://localhost:3001/api
- auth_url: http://localhost:3000/auth
- access_token: (se obtiene del login)

### Pre-request Script para Auto-login:
```javascript
if (!pm.environment.get("access_token")) {
    pm.sendRequest({
        url: pm.environment.get("auth_url") + "/login",
        method: 'POST',
        header: {
            'Content-Type': 'application/json',
        },
        body: {
            mode: 'raw',
            raw: JSON.stringify({
                email: "<EMAIL>",
                password: "password123"
            })
        }
    }, function (err, response) {
        if (response.json().access_token) {
            pm.environment.set("access_token", response.json().access_token);
        }
    });
}
```

### Test Script para Validar Respuestas:
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has required fields", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('_id');
    pm.expect(jsonData).to.have.property('createdAt');
});
```

## 10. DOCUMENTACIÓN SWAGGER
Una vez iniciado el servidor, accede a:
http://localhost:3001/api/docs

## 11. COMANDOS PARA INICIAR EL PROYECTO
```bash
# Instalar dependencias
npm install

# Configurar entorno
cp .env.example .env

# Iniciar MongoDB (si no está corriendo)
mongod

# Iniciar en modo desarrollo
npm run start:dev
```

## NOTAS IMPORTANTES:
- Todos los endpoints requieren autenticación JWT excepto /health
- Los IDs de MongoDB son ObjectIds de 24 caracteres hexadecimales
- Las fechas están en formato ISO 8601
- Los precios son números decimales con 2 decimales
- Los ratings van de 1 a 5
- El storeId se asigna automáticamente desde el token JWT
