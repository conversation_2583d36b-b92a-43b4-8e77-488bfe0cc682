import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from './entities/product.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { User } from '../common/interfaces/user.interface';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product)
    private productsRepository: Repository<Product>,
  ) {}

  async create(createProductDto: CreateProductDto, user: User): Promise<Product> {
    if (user.role !== 'store') {
      throw new ForbiddenException('Only store users can create products');
    }

    const product = this.productsRepository.create({
      ...createProductDto,
      storeId: user.id,
    });

    return this.productsRepository.save(product);
  }

  async findAll(user: User): Promise<Product[]> {
    if (user.role !== 'store') {
      throw new ForbiddenException('Only store users can access products');
    }

    return this.productsRepository.find({
      where: { storeId: user.id },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string, user: User): Promise<Product> {
    const product = await this.productsRepository.findOne({
      where: { id, storeId: user.id },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return product;
  }

  async update(id: string, updateProductDto: UpdateProductDto, user: User): Promise<Product> {
    const product = await this.findOne(id, user);
    
    Object.assign(product, updateProductDto);
    return this.productsRepository.save(product);
  }

  async remove(id: string, user: User): Promise<void> {
    const product = await this.findOne(id, user);
    await this.productsRepository.remove(product);
  }
}
