import { Document } from 'mongoose';
export declare class Supplier extends Document {
    name: string;
    contactPerson: string;
    email: string;
    phone: string;
    address: string;
    products: string[];
    storeId: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare const SupplierSchema: import("mongoose").Schema<Supplier, import("mongoose").Model<Supplier, any, any, any, Document<unknown, any, Supplier, any> & Supplier & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Supplier, Document<unknown, {}, import("mongoose").FlatRecord<Supplier>, {}> & import("mongoose").FlatRecord<Supplier> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
