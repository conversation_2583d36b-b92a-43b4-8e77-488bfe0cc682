import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { User } from '../common/interfaces/user.interface';
export declare class ProductsController {
    private readonly productsService;
    constructor(productsService: ProductsService);
    create(createProductDto: CreateProductDto, user: User): Promise<import("./entities/product.entity").Product>;
    findAll(user: User): Promise<import("./entities/product.entity").Product[]>;
    findOne(id: string, user: User): Promise<import("./entities/product.entity").Product>;
    update(id: string, updateProductDto: UpdateProductDto, user: User): Promise<import("./entities/product.entity").Product>;
    remove(id: string, user: User): Promise<void>;
}
