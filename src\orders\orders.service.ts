import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order } from './entities/order.entity';
import { UpdateOrderStatusDto } from './dto/update-order-status.dto';
import { User } from '../common/interfaces/user.interface';

@Injectable()
export class OrdersService {
  constructor(
    @InjectRepository(Order)
    private ordersRepository: Repository<Order>,
  ) {}

  async findAll(user: User): Promise<Order[]> {
    if (user.role !== 'store') {
      throw new ForbiddenException('Only store users can access orders');
    }

    return this.ordersRepository.find({
      where: { storeId: user.id },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string, user: User): Promise<Order> {
    if (user.role !== 'store') {
      throw new ForbiddenException('Only store users can access orders');
    }

    const order = await this.ordersRepository.findOne({
      where: { id, storeId: user.id },
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    return order;
  }

  async updateStatus(id: string, updateOrderStatusDto: UpdateOrderStatusDto, user: User): Promise<Order> {
    const order = await this.findOne(id, user);
    
    order.status = updateOrderStatusDto.status;
    return this.ordersRepository.save(order);
  }

  async getOrdersByStatus(status: string, user: User): Promise<Order[]> {
    if (user.role !== 'store') {
      throw new ForbiddenException('Only store users can access orders');
    }

    return this.ordersRepository.find({
      where: { storeId: user.id, status: status as any },
      order: { createdAt: 'DESC' },
    });
  }
}
