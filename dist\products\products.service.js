"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const product_entity_1 = require("./entities/product.entity");
let ProductsService = class ProductsService {
    constructor(productModel) {
        this.productModel = productModel;
    }
    async create(createProductDto, user) {
        if (user.role !== 'store') {
            throw new common_1.ForbiddenException('Only store users can create products');
        }
        const product = new this.productModel({
            ...createProductDto,
            storeId: user.id,
        });
        return product.save();
    }
    async findAll(user) {
        if (user.role !== 'store') {
            throw new common_1.ForbiddenException('Only store users can access products');
        }
        return this.productModel.find({ storeId: user.id }).sort({ createdAt: -1 }).exec();
    }
    async findOne(id, user) {
        const product = await this.productModel.findOne({ _id: id, storeId: user.id }).exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        return product;
    }
    async update(id, updateProductDto, user) {
        const product = await this.productModel.findOneAndUpdate({ _id: id, storeId: user.id }, updateProductDto, { new: true }).exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        return product;
    }
    async remove(id, user) {
        const result = await this.productModel.deleteOne({ _id: id, storeId: user.id }).exec();
        if (result.deletedCount === 0) {
            throw new common_1.NotFoundException('Product not found');
        }
    }
};
exports.ProductsService = ProductsService;
exports.ProductsService = ProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(product_entity_1.Product.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], ProductsService);
//# sourceMappingURL=products.service.js.map