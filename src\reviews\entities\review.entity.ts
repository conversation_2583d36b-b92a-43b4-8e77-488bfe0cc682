import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from 'typeorm';

@Entity('reviews')
export class Review {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  customer: string;

  @Column('int')
  rating: number;

  @Column('text')
  comment: string;

  @Column({ nullable: true })
  productId?: string;

  @Column({ nullable: true })
  productName?: string;

  @Column()
  storeId: string;

  @CreateDateColumn()
  createdAt: Date;
}
