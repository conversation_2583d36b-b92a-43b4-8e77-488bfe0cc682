import { Injectable, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from '../products/entities/product.entity';
import { Order } from '../orders/entities/order.entity';
import { Review } from '../reviews/entities/review.entity';
import { DashboardStatsDto } from './dto/dashboard-stats.dto';
import { User } from '../common/interfaces/user.interface';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(Product)
    private productsRepository: Repository<Product>,
    @InjectRepository(Order)
    private ordersRepository: Repository<Order>,
    @InjectRepository(Review)
    private reviewsRepository: Repository<Review>,
  ) {}

  async getDashboardStats(user: User): Promise<DashboardStatsDto> {
    if (user.role !== 'store') {
      throw new ForbiddenException('Only store users can access dashboard');
    }

    const [
      totalProducts,
      pendingOrders,
      completedOrders,
      totalRevenueResult,
      averageRatingResult,
    ] = await Promise.all([
      this.productsRepository.count({ where: { storeId: user.id } }),
      this.ordersRepository.count({ 
        where: { storeId: user.id, status: 'pending' } 
      }),
      this.ordersRepository.count({ 
        where: { storeId: user.id, status: 'delivered' } 
      }),
      this.ordersRepository
        .createQueryBuilder('order')
        .select('SUM(order.total)', 'total')
        .where('order.storeId = :storeId', { storeId: user.id })
        .andWhere('order.status = :status', { status: 'delivered' })
        .getRawOne(),
      this.reviewsRepository
        .createQueryBuilder('review')
        .select('AVG(review.rating)', 'average')
        .where('review.storeId = :storeId', { storeId: user.id })
        .getRawOne(),
    ]);

    return {
      totalProducts,
      pendingOrders,
      completedOrders,
      totalRevenue: parseFloat(totalRevenueResult?.total || '0'),
      averageRating: parseFloat(averageRatingResult?.average || '0'),
    };
  }
}
