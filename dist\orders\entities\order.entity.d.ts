import { Document } from 'mongoose';
export interface Customer {
    name: string;
    address?: string;
    phone: string;
    email?: string;
}
export interface OrderItem {
    id: string;
    name: string;
    quantity: number;
    price: number;
}
export type OrderStatus = 'pending' | 'processing' | 'ready' | 'delivered' | 'cancelled';
export type DeliveryMethod = 'delivery' | 'pickup';
export declare class Order extends Document {
    customer: Customer;
    total: number;
    status: OrderStatus;
    deliveryMethod: DeliveryMethod;
    items: OrderItem[];
    notes?: string;
    storeId: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare const OrderSchema: import("mongoose").Schema<Order, import("mongoose").Model<Order, any, any, any, Document<unknown, any, Order, any> & Order & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Order, Document<unknown, {}, import("mongoose").FlatRecord<Order>, {}> & import("mongoose").FlatRecord<Order> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
