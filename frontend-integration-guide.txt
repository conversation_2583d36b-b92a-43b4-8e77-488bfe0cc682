# GUÍA DE INTEGRACIÓN FRONTEND - STORE MICROSERVICE
# =====================================================

## CONFIGURACIÓN BASE
Base URL: http://localhost:3001/api
Auth Header: Authorization: Bearer {token}

## 1. AUTENTICACIÓN
### Obtener Token (desde microservicio de auth)
Endpoint: POST http://localhost:3000/auth/login
Body: {
  "email": "<EMAIL>",
  "password": "password123"
}
Response: { "access_token": "jwt_token_here", "user": {...} }

## 2. DASHBOARD - PÁGINA PRINCIPAL
### Obtener Estadísticas
Endpoint: GET /store/dashboard
Headers: { "Authorization": "Bearer {token}" }
Response: {
  "totalProducts": 25,
  "pendingOrders": 8,
  "completedOrders": 142,
  "totalRevenue": 15750.50,
  "averageRating": 4.3
}

Frontend Usage:
- Mostrar cards con las estadísticas
- Gráficos de ventas y pedidos
- Indicadores de rendimiento

## 3. PRODUCTOS - GESTIÓN DE INVENTARIO

### 3.1 Listar Productos
Endpoint: GET /store/products
Headers: { "Authorization": "Bearer {token}" }
Response: Array de productos

Frontend Usage:
- Tabla de productos con paginación
- Filtros por categoría, stock bajo
- Búsqueda por nombre

### 3.2 Crear Producto
Endpoint: POST /store/products
Headers: { "Authorization": "Bearer {token}", "Content-Type": "application/json" }
Body: {
  "name": "Pizza Margherita",
  "description": "Pizza clásica italiana",
  "price": 12.99,
  "stock": 50,
  "category": "Pizzas",
  "image": "url_de_imagen",
  "tags": ["vegetariano", "clásico"],
  "deliveryOptions": {
    "delivery": true,
    "pickup": true
  },
  "nutritionalInfo": {
    "calories": 280,
    "protein": 12,
    "carbs": 35,
    "fat": 8
  }
}

Frontend Form Fields:
- name: input text (required)
- description: textarea (required)
- price: input number, min=0 (required)
- stock: input number, min=0 (required)
- category: select dropdown (required)
- image: file upload o input url (required)
- tags: input chips/tags (array)
- deliveryOptions.delivery: checkbox
- deliveryOptions.pickup: checkbox
- nutritionalInfo: campos opcionales numéricos

### 3.3 Actualizar Producto
Endpoint: PATCH /store/products/{id}
Headers: { "Authorization": "Bearer {token}", "Content-Type": "application/json" }
Body: Campos a actualizar (parcial)

Frontend Usage:
- Modal de edición con formulario pre-llenado
- Validación en tiempo real
- Confirmación antes de guardar

### 3.4 Eliminar Producto
Endpoint: DELETE /store/products/{id}
Headers: { "Authorization": "Bearer {token}" }

Frontend Usage:
- Botón de eliminar con confirmación
- Modal de confirmación "¿Estás seguro?"
- Actualizar lista después de eliminar

## 4. PEDIDOS - GESTIÓN DE ÓRDENES

### 4.1 Listar Pedidos
Endpoint: GET /store/orders
Headers: { "Authorization": "Bearer {token}" }
Query Params: ?status=pending (opcional)

Frontend Usage:
- Tabla de pedidos con filtros por estado
- Badges de colores por estado
- Ordenar por fecha más reciente

### 4.2 Ver Detalle de Pedido
Endpoint: GET /store/orders/{id}
Headers: { "Authorization": "Bearer {token}" }

Frontend Usage:
- Modal o página de detalle
- Mostrar información del cliente
- Lista de productos pedidos
- Total y método de entrega

### 4.3 Actualizar Estado de Pedido
Endpoint: PATCH /store/orders/{id}/status
Headers: { "Authorization": "Bearer {token}", "Content-Type": "application/json" }
Body: { "status": "processing" }

Estados válidos: "pending", "processing", "ready", "delivered", "cancelled"

Frontend Usage:
- Dropdown o botones para cambiar estado
- Flujo visual del estado del pedido
- Notificaciones al cliente (opcional)

## 5. PROVEEDORES - GESTIÓN DE SUPPLIERS

### 5.1 Listar Proveedores
Endpoint: GET /store/suppliers
Headers: { "Authorization": "Bearer {token}" }

Frontend Usage:
- Tabla con información de contacto
- Búsqueda por nombre o productos
- Acciones de editar/eliminar

### 5.2 Crear Proveedor
Endpoint: POST /store/suppliers
Headers: { "Authorization": "Bearer {token}", "Content-Type": "application/json" }
Body: {
  "name": "Distribuidora La Italiana",
  "contactPerson": "Marco Rossi",
  "email": "<EMAIL>",
  "phone": "+1234567891",
  "address": "Zona Industrial 789",
  "products": ["queso mozzarella", "tomate san marzano"]
}

Frontend Form Fields:
- name: input text (required)
- contactPerson: input text (required)
- email: input email (required)
- phone: input tel (required)
- address: textarea (required)
- products: input chips/tags para array de strings

### 5.3 Actualizar Proveedor
Endpoint: PATCH /store/suppliers/{id}
Headers: { "Authorization": "Bearer {token}", "Content-Type": "application/json" }
Body: Campos a actualizar

### 5.4 Eliminar Proveedor
Endpoint: DELETE /store/suppliers/{id}
Headers: { "Authorization": "Bearer {token}" }

## 6. REVIEWS - VISUALIZACIÓN DE RESEÑAS

### 6.1 Obtener Reviews
Endpoint: GET /store/reviews
Headers: { "Authorization": "Bearer {token}" }
Response: {
  "reviews": [...],
  "averageRating": 4.3,
  "totalReviews": 87
}

Frontend Usage:
- Lista de reseñas con paginación
- Mostrar rating con estrellas
- Filtros por rating o producto
- Estadísticas de satisfacción

## 7. MANEJO DE ERRORES EN FRONTEND

### 7.1 Error 401 - No Autorizado
Acción: Redirigir a login, limpiar token almacenado

### 7.2 Error 403 - Sin Permisos
Acción: Mostrar mensaje "No tienes permisos", redirigir a dashboard

### 7.3 Error 404 - No Encontrado
Acción: Mostrar "Recurso no encontrado", botón para volver

### 7.4 Error 400 - Validación
Acción: Mostrar errores en formulario, resaltar campos inválidos

### 7.5 Error 500 - Error del Servidor
Acción: Mostrar "Error interno", botón para reintentar

## 8. ESTADOS DE CARGA Y UX

### Loading States:
- Skeleton loaders para tablas
- Spinners para acciones (guardar, eliminar)
- Disable botones durante requests

### Success States:
- Toasts/notifications de éxito
- Actualización automática de listas
- Feedback visual inmediato

### Empty States:
- "No hay productos" con botón "Agregar producto"
- "No hay pedidos" con ilustración
- "No hay proveedores" con call-to-action

## 9. VALIDACIONES EN FRONTEND

### Productos:
- Precio > 0
- Stock >= 0
- Nombre no vacío
- Imagen URL válida o archivo
- Al menos una opción de entrega

### Proveedores:
- Email válido
- Teléfono formato correcto
- Al menos un producto en la lista

### Pedidos:
- Solo cambios de estado válidos
- Confirmación para cancelar pedidos

## 10. OPTIMIZACIONES RECOMENDADAS

### Caching:
- Cache de productos para evitar requests repetidos
- Invalidar cache al crear/actualizar

### Paginación:
- Implementar paginación en listas largas
- Lazy loading para mejor performance

### Real-time:
- WebSockets para pedidos en tiempo real
- Notificaciones push para nuevos pedidos

### Offline:
- Service workers para funcionalidad offline básica
- Sync cuando vuelva la conexión

## 11. EJEMPLOS DE CÓDIGO FRONTEND

### 11.1 Servicio de API (JavaScript/TypeScript)
```javascript
class StoreAPI {
  constructor(baseURL = 'http://localhost:3001/api') {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('access_token');
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Dashboard
  async getDashboardStats() {
    return this.request('/store/dashboard');
  }

  // Productos
  async getProducts() {
    return this.request('/store/products');
  }

  async createProduct(productData) {
    return this.request('/store/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    });
  }

  async updateProduct(id, productData) {
    return this.request(`/store/products/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(productData),
    });
  }

  async deleteProduct(id) {
    return this.request(`/store/products/${id}`, {
      method: 'DELETE',
    });
  }

  // Pedidos
  async getOrders(status = null) {
    const query = status ? `?status=${status}` : '';
    return this.request(`/store/orders${query}`);
  }

  async updateOrderStatus(id, status) {
    return this.request(`/store/orders/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
    });
  }
}
```

### 11.2 Hook de React para Productos
```javascript
import { useState, useEffect } from 'react';

export const useProducts = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const api = new StoreAPI();

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const data = await api.getProducts();
      setProducts(data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const createProduct = async (productData) => {
    try {
      const newProduct = await api.createProduct(productData);
      setProducts(prev => [newProduct, ...prev]);
      return newProduct;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const updateProduct = async (id, productData) => {
    try {
      const updatedProduct = await api.updateProduct(id, productData);
      setProducts(prev =>
        prev.map(p => p._id === id ? updatedProduct : p)
      );
      return updatedProduct;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const deleteProduct = async (id) => {
    try {
      await api.deleteProduct(id);
      setProducts(prev => prev.filter(p => p._id !== id));
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  return {
    products,
    loading,
    error,
    createProduct,
    updateProduct,
    deleteProduct,
    refetch: fetchProducts,
  };
};
```

### 11.3 Componente de Formulario de Producto (React)
```javascript
import React, { useState } from 'react';

export const ProductForm = ({ product = null, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    name: product?.name || '',
    description: product?.description || '',
    price: product?.price || 0,
    stock: product?.stock || 0,
    category: product?.category || '',
    image: product?.image || '',
    tags: product?.tags || [],
    deliveryOptions: {
      delivery: product?.deliveryOptions?.delivery || false,
      pickup: product?.deliveryOptions?.pickup || false,
    },
    nutritionalInfo: {
      calories: product?.nutritionalInfo?.calories || 0,
      protein: product?.nutritionalInfo?.protein || 0,
      carbs: product?.nutritionalInfo?.carbs || 0,
      fat: product?.nutritionalInfo?.fat || 0,
    },
  });

  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'El nombre es requerido';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La descripción es requerida';
    }

    if (formData.price <= 0) {
      newErrors.price = 'El precio debe ser mayor a 0';
    }

    if (formData.stock < 0) {
      newErrors.stock = 'El stock no puede ser negativo';
    }

    if (!formData.deliveryOptions.delivery && !formData.deliveryOptions.pickup) {
      newErrors.deliveryOptions = 'Debe seleccionar al menos una opción de entrega';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="product-form">
      <div className="form-group">
        <label>Nombre *</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          className={errors.name ? 'error' : ''}
        />
        {errors.name && <span className="error-text">{errors.name}</span>}
      </div>

      <div className="form-group">
        <label>Precio *</label>
        <input
          type="number"
          step="0.01"
          min="0"
          value={formData.price}
          onChange={(e) => handleInputChange('price', parseFloat(e.target.value))}
          className={errors.price ? 'error' : ''}
        />
        {errors.price && <span className="error-text">{errors.price}</span>}
      </div>

      <div className="form-group">
        <label>Opciones de Entrega *</label>
        <div className="checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={formData.deliveryOptions.delivery}
              onChange={(e) => handleInputChange('deliveryOptions', {
                ...formData.deliveryOptions,
                delivery: e.target.checked,
              })}
            />
            Delivery
          </label>
          <label>
            <input
              type="checkbox"
              checked={formData.deliveryOptions.pickup}
              onChange={(e) => handleInputChange('deliveryOptions', {
                ...formData.deliveryOptions,
                pickup: e.target.checked,
              })}
            />
            Pickup
          </label>
        </div>
        {errors.deliveryOptions && (
          <span className="error-text">{errors.deliveryOptions}</span>
        )}
      </div>

      <div className="form-actions">
        <button type="button" onClick={onCancel}>
          Cancelar
        </button>
        <button type="submit">
          {product ? 'Actualizar' : 'Crear'} Producto
        </button>
      </div>
    </form>
  );
};
```

## 12. ESTRUCTURA DE PÁGINAS RECOMENDADA

### Dashboard (/dashboard)
- Cards con estadísticas principales
- Gráfico de ventas del mes
- Lista de pedidos recientes
- Productos con stock bajo

### Productos (/products)
- Tabla con filtros y búsqueda
- Botón "Agregar Producto"
- Acciones: Ver, Editar, Eliminar
- Modal para crear/editar

### Pedidos (/orders)
- Tabs por estado (Pendientes, En Proceso, Listos, etc.)
- Filtros por fecha y método de entrega
- Vista de detalle en modal
- Actualización de estado inline

### Proveedores (/suppliers)
- Lista con información de contacto
- Búsqueda por nombre o productos
- Formulario de creación/edición

### Reviews (/reviews)
- Lista de reseñas con rating visual
- Filtros por rating y producto
- Estadísticas de satisfacción
- Solo lectura (las reviews las crean los clientes)

## 13. CONSIDERACIONES DE SEGURIDAD

### Token Management:
- Almacenar token en localStorage o sessionStorage
- Renovar token antes de que expire
- Limpiar token al hacer logout

### Validación:
- Validar en frontend Y backend
- Sanitizar inputs antes de enviar
- Escapar contenido al mostrar

### HTTPS:
- Usar HTTPS en producción
- Configurar CORS correctamente
- Headers de seguridad apropiados
