import { Model } from 'mongoose';
import { Supplier } from './entities/supplier.entity';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { User } from '../common/interfaces/user.interface';
export declare class SuppliersService {
    private supplierModel;
    constructor(supplierModel: Model<Supplier>);
    create(createSupplierDto: CreateSupplierDto, user: User): Promise<Supplier>;
    findAll(user: User): Promise<Supplier[]>;
    findOne(id: string, user: User): Promise<Supplier>;
    update(id: string, updateSupplierDto: UpdateSupplierDto, user: User): Promise<Supplier>;
    remove(id: string, user: User): Promise<void>;
}
