# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=store_microservice

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# Application Configuration
PORT=3001
NODE_ENV=development

# Auth Microservice URL (if needed for validation)
AUTH_SERVICE_URL=http://localhost:3000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
