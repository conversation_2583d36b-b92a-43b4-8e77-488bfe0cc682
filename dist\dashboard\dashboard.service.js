"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const product_entity_1 = require("../products/entities/product.entity");
const order_entity_1 = require("../orders/entities/order.entity");
const review_entity_1 = require("../reviews/entities/review.entity");
let DashboardService = class DashboardService {
    constructor(productModel, orderModel, reviewModel) {
        this.productModel = productModel;
        this.orderModel = orderModel;
        this.reviewModel = reviewModel;
    }
    async getDashboardStats(user) {
        if (user.role !== 'store') {
            throw new common_1.ForbiddenException('Only store users can access dashboard');
        }
        const [totalProducts, pendingOrders, completedOrders, totalRevenueResult, averageRatingResult,] = await Promise.all([
            this.productModel.countDocuments({ storeId: user.id }).exec(),
            this.orderModel.countDocuments({ storeId: user.id, status: 'pending' }).exec(),
            this.orderModel.countDocuments({ storeId: user.id, status: 'delivered' }).exec(),
            this.orderModel.aggregate([
                { $match: { storeId: user.id, status: 'delivered' } },
                { $group: { _id: null, total: { $sum: '$total' } } }
            ]).exec(),
            this.reviewModel.aggregate([
                { $match: { storeId: user.id } },
                { $group: { _id: null, average: { $avg: '$rating' } } }
            ]).exec(),
        ]);
        return {
            totalProducts,
            pendingOrders,
            completedOrders,
            totalRevenue: totalRevenueResult.length > 0 ? totalRevenueResult[0].total : 0,
            averageRating: averageRatingResult.length > 0 ? averageRatingResult[0].average : 0,
        };
    }
};
exports.DashboardService = DashboardService;
exports.DashboardService = DashboardService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(product_entity_1.Product.name)),
    __param(1, (0, mongoose_1.InjectModel)(order_entity_1.Order.name)),
    __param(2, (0, mongoose_1.InjectModel)(review_entity_1.Review.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model])
], DashboardService);
//# sourceMappingURL=dashboard.service.js.map