import { Model } from 'mongoose';
import { Product } from '../products/entities/product.entity';
import { Order } from '../orders/entities/order.entity';
import { Review } from '../reviews/entities/review.entity';
import { DashboardStatsDto } from './dto/dashboard-stats.dto';
import { User } from '../common/interfaces/user.interface';
export declare class DashboardService {
    private productModel;
    private orderModel;
    private reviewModel;
    constructor(productModel: Model<Product>, orderModel: Model<Order>, reviewModel: Model<Review>);
    getDashboardStats(user: User): Promise<DashboardStatsDto>;
}
