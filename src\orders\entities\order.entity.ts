import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export interface Customer {
  name: string;
  address?: string;
  phone: string;
  email?: string;
}

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
}

export type OrderStatus = 'pending' | 'processing' | 'ready' | 'delivered' | 'cancelled';
export type DeliveryMethod = 'delivery' | 'pickup';

@Entity('orders')
export class Order {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('json')
  customer: Customer;

  @Column('decimal', { precision: 10, scale: 2 })
  total: number;

  @Column({
    type: 'enum',
    enum: ['pending', 'processing', 'ready', 'delivered', 'cancelled'],
    default: 'pending',
  })
  status: OrderStatus;

  @Column({
    type: 'enum',
    enum: ['delivery', 'pickup'],
  })
  deliveryMethod: DeliveryMethod;

  @Column('json')
  items: OrderItem[];

  @Column('text', { nullable: true })
  notes?: string;

  @Column()
  storeId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
