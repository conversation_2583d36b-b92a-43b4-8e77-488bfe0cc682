import { OrdersService } from './orders.service';
import { UpdateOrderStatusDto } from './dto/update-order-status.dto';
import { User } from '../common/interfaces/user.interface';
export declare class OrdersController {
    private readonly ordersService;
    constructor(ordersService: OrdersService);
    findAll(user: User, status?: string): Promise<import("./entities/order.entity").Order[]>;
    findOne(id: string, user: User): Promise<import("./entities/order.entity").Order>;
    updateStatus(id: string, updateOrderStatusDto: UpdateOrderStatusDto, user: User): Promise<import("./entities/order.entity").Order>;
}
