import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, IsObject, IsOptional, Min, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class DeliveryOptionsDto {
  @ApiProperty()
  @IsBoolean()
  delivery: boolean;

  @ApiProperty()
  @IsBoolean()
  pickup: boolean;
}

export class NutritionalInfoDto {
  @ApiProperty()
  @IsNumber()
  @Min(0)
  calories: number;

  @ApiProperty()
  @IsNumber()
  @Min(0)
  protein: number;

  @ApiProperty()
  @IsNumber()
  @Min(0)
  carbs: number;

  @ApiProperty()
  @IsNumber()
  @Min(0)
  fat: number;
}

export class CreateProductDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty()
  @IsN<PERSON>ber()
  @Min(0)
  stock: number;

  @ApiProperty()
  @IsString()
  category: string;

  @ApiProperty()
  @IsString()
  image: string;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  tags: string[];

  @ApiProperty({ type: DeliveryOptionsDto })
  @IsObject()
  @Type(() => DeliveryOptionsDto)
  deliveryOptions: DeliveryOptionsDto;

  @ApiPropertyOptional({ type: NutritionalInfoDto })
  @IsOptional()
  @IsObject()
  @Type(() => NutritionalInfoDto)
  nutritionalInfo?: NutritionalInfoDto;
}
