import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Supplier } from './entities/supplier.entity';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { User } from '../common/interfaces/user.interface';

@Injectable()
export class SuppliersService {
  constructor(
    @InjectRepository(Supplier)
    private suppliersRepository: Repository<Supplier>,
  ) {}

  async create(createSupplierDto: CreateSupplierDto, user: User): Promise<Supplier> {
    if (user.role !== 'store') {
      throw new ForbiddenException('Only store users can create suppliers');
    }

    const supplier = this.suppliersRepository.create({
      ...createSupplierDto,
      storeId: user.id,
    });

    return this.suppliersRepository.save(supplier);
  }

  async findAll(user: User): Promise<Supplier[]> {
    if (user.role !== 'store') {
      throw new ForbiddenException('Only store users can access suppliers');
    }

    return this.suppliersRepository.find({
      where: { storeId: user.id },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string, user: User): Promise<Supplier> {
    const supplier = await this.suppliersRepository.findOne({
      where: { id, storeId: user.id },
    });

    if (!supplier) {
      throw new NotFoundException('Supplier not found');
    }

    return supplier;
  }

  async update(id: string, updateSupplierDto: UpdateSupplierDto, user: User): Promise<Supplier> {
    const supplier = await this.findOne(id, user);
    
    Object.assign(supplier, updateSupplierDto);
    return this.suppliersRepository.save(supplier);
  }

  async remove(id: string, user: User): Promise<void> {
    const supplier = await this.findOne(id, user);
    await this.suppliersRepository.remove(supplier);
  }
}
