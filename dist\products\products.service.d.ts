import { Model } from 'mongoose';
import { Product } from './entities/product.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { User } from '../common/interfaces/user.interface';
export declare class ProductsService {
    private productModel;
    constructor(productModel: Model<Product>);
    create(createProductDto: CreateProductDto, user: User): Promise<Product>;
    findAll(user: User): Promise<Product[]>;
    findOne(id: string, user: User): Promise<Product>;
    update(id: string, updateProductDto: UpdateProductDto, user: User): Promise<Product>;
    remove(id: string, user: User): Promise<void>;
}
