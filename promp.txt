// tienda-microservicio.txt

# Microservicio: Tienda (Locatario)

## Endpoints API necesarios

- `GET /api/store/dashboard`: Obtener estadísticas del dashboard
- `GET /api/store/products`: Obtener productos de la tienda
- `POST /api/store/products`: Crear un nuevo producto
- `PUT /api/store/products/:id`: Actualizar un producto existente
- `DELETE /api/store/products/:id`: Eliminar un producto
- `GET /api/store/orders`: Obtener pedidos de la tienda
- `GET /api/store/orders/:id`: Obtener detalles de un pedido específico
- `PUT /api/store/orders/:id/status`: Actualizar el estado de un pedido
- `GET /api/store/suppliers`: Obtener proveedores de la tienda
- `POST /api/store/suppliers`: Crear un nuevo proveedor
- `PUT /api/store/suppliers/:id`: Actualizar un proveedor existente
- `DELETE /api/store/suppliers/:id`: Eliminar un proveedor
- `GET /api/store/reviews`: Obtener reseñas de la tienda

## Modelos de datos sugeridos

### Usuario (Tienda)
- id: string
- name: string
- email: string
- password: string (hash)
- phone: string
- address: string
- role: 'store'
- storeData: {
    storeName: string
    storeAddress: string
    storePhone: string
  }

### DashboardStats
- totalProducts: number
- pendingOrders: number
- completedOrders: number
- totalRevenue: number
- averageRating: number

### StoreProduct
- id: string
- name: string
- description: string
- price: number
- stock: number
- category: string
- image: string
- tags: string[]
- deliveryOptions: {
    delivery: boolean
    pickup: boolean
  }
- nutritionalInfo?: {
    calories: number
    protein: number
    carbs: number
    fat: number
  }

### Order
- id: string
- customer: {
    name: string
    address?: string
    phone: string
    email?: string
  }
- date: string
- total: number
- status: 'pending' | 'processing' | 'ready' | 'delivered' | 'cancelled'
- deliveryMethod: 'delivery' | 'pickup'
- items: {
    id: string
    name: string
    quantity: number
    price: number
  }[]
- notes?: string

### Supplier
- id: string
- name: string
- contactPerson: string
- email: string
- phone: string
- address: string
- products: string[]

### StoreReview
- id: string
- customer: string
- rating: number
- comment: string
- date: string
- productId?: string
- productName?: string

### ReviewsData
- reviews: StoreReview[]
- averageRating: number
- totalReviews: number

## Consideraciones
- Autenticación JWT para tiendas.
- Validación de datos en endpoints de productos, pedidos y proveedores.
- Relación con microservicio de cliente para pedidos y reseñas.