import { Model } from 'mongoose';
import { Order } from './entities/order.entity';
import { UpdateOrderStatusDto } from './dto/update-order-status.dto';
import { User } from '../common/interfaces/user.interface';
export declare class OrdersService {
    private orderModel;
    constructor(orderModel: Model<Order>);
    findAll(user: User): Promise<Order[]>;
    findOne(id: string, user: User): Promise<Order>;
    updateStatus(id: string, updateOrderStatusDto: UpdateOrderStatusDto, user: User): Promise<Order>;
    getOrdersByStatus(status: string, user: User): Promise<Order[]>;
}
