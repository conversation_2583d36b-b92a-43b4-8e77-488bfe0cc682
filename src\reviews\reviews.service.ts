import { Injectable, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Review } from './entities/review.entity';
import { ReviewsDataDto } from './dto/reviews-data.dto';
import { User } from '../common/interfaces/user.interface';

@Injectable()
export class ReviewsService {
  constructor(
    @InjectRepository(Review)
    private reviewsRepository: Repository<Review>,
  ) {}

  async findAll(user: User): Promise<ReviewsDataDto> {
    if (user.role !== 'store') {
      throw new ForbiddenException('Only store users can access reviews');
    }

    const [reviews, averageResult] = await Promise.all([
      this.reviewsRepository.find({
        where: { storeId: user.id },
        order: { createdAt: 'DESC' },
      }),
      this.reviewsRepository
        .createQueryBuilder('review')
        .select('AVG(review.rating)', 'average')
        .where('review.storeId = :storeId', { storeId: user.id })
        .getRawOne(),
    ]);

    return {
      reviews,
      averageRating: parseFloat(averageResult?.average || '0'),
      totalReviews: reviews.length,
    };
  }
}
