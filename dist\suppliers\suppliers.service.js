"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuppliersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const supplier_entity_1 = require("./entities/supplier.entity");
let SuppliersService = class SuppliersService {
    constructor(supplierModel) {
        this.supplierModel = supplierModel;
    }
    async create(createSupplierDto, user) {
        if (user.role !== 'store') {
            throw new common_1.ForbiddenException('Only store users can create suppliers');
        }
        const supplier = new this.supplierModel({
            ...createSupplierDto,
            storeId: user.id,
        });
        return supplier.save();
    }
    async findAll(user) {
        if (user.role !== 'store') {
            throw new common_1.ForbiddenException('Only store users can access suppliers');
        }
        return this.supplierModel.find({ storeId: user.id }).sort({ createdAt: -1 }).exec();
    }
    async findOne(id, user) {
        const supplier = await this.supplierModel.findOne({ _id: id, storeId: user.id }).exec();
        if (!supplier) {
            throw new common_1.NotFoundException('Supplier not found');
        }
        return supplier;
    }
    async update(id, updateSupplierDto, user) {
        const supplier = await this.supplierModel.findOneAndUpdate({ _id: id, storeId: user.id }, updateSupplierDto, { new: true }).exec();
        if (!supplier) {
            throw new common_1.NotFoundException('Supplier not found');
        }
        return supplier;
    }
    async remove(id, user) {
        const result = await this.supplierModel.deleteOne({ _id: id, storeId: user.id }).exec();
        if (result.deletedCount === 0) {
            throw new common_1.NotFoundException('Supplier not found');
        }
    }
};
exports.SuppliersService = SuppliersService;
exports.SuppliersService = SuppliersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(supplier_entity_1.Supplier.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], SuppliersService);
//# sourceMappingURL=suppliers.service.js.map