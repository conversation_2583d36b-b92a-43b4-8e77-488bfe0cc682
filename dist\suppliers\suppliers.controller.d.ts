import { SuppliersService } from './suppliers.service';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { User } from '../common/interfaces/user.interface';
export declare class SuppliersController {
    private readonly suppliersService;
    constructor(suppliersService: SuppliersService);
    create(createSupplierDto: CreateSupplierDto, user: User): Promise<import("./entities/supplier.entity").Supplier>;
    findAll(user: User): Promise<import("./entities/supplier.entity").Supplier[]>;
    findOne(id: string, user: User): Promise<import("./entities/supplier.entity").Supplier>;
    update(id: string, updateSupplierDto: UpdateSupplierDto, user: User): Promise<import("./entities/supplier.entity").Supplier>;
    remove(id: string, user: User): Promise<void>;
}
