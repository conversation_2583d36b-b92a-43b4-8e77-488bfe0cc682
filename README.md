# Store Microservice

A NestJS microservice for managing store operations including products, orders, suppliers, and dashboard analytics.

## Features

- **Products Management**: CRUD operations for store products
- **Orders Management**: View and update order status
- **Suppliers Management**: Manage store suppliers
- **Dashboard**: Analytics and statistics
- **Reviews**: View store reviews and ratings
- **JWT Authentication**: Secure endpoints with JWT tokens
- **Swagger Documentation**: Auto-generated API documentation
- **TypeORM**: Database ORM with MySQL support

## API Endpoints

### Health Check
- `GET /api/health` - Health check endpoint

### Dashboard
- `GET /api/store/dashboard` - Get dashboard statistics

### Products
- `GET /api/store/products` - Get all products
- `POST /api/store/products` - Create a new product
- `GET /api/store/products/:id` - Get a specific product
- `PATCH /api/store/products/:id` - Update a product
- `DELETE /api/store/products/:id` - Delete a product

### Orders
- `GET /api/store/orders` - Get all orders (with optional status filter)
- `GET /api/store/orders/:id` - Get a specific order
- `PATCH /api/store/orders/:id/status` - Update order status

### Suppliers
- `GET /api/store/suppliers` - Get all suppliers
- `POST /api/store/suppliers` - Create a new supplier
- `GET /api/store/suppliers/:id` - Get a specific supplier
- `PATCH /api/store/suppliers/:id` - Update a supplier
- `DELETE /api/store/suppliers/:id` - Delete a supplier

### Reviews
- `GET /api/store/reviews` - Get all reviews with statistics

## Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- MySQL database
- Your existing auth microservice running

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Environment Configuration:**
   ```bash
   cp .env.example .env
   ```
   
   Update the `.env` file with your configuration:
   ```env
   # Database Configuration
   DB_HOST=localhost
   DB_PORT=3306
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   DB_DATABASE=store_microservice

   # JWT Configuration (should match your auth microservice)
   JWT_SECRET=your-super-secret-jwt-key-here
   JWT_EXPIRES_IN=24h

   # Application Configuration
   PORT=3001
   NODE_ENV=development

   # Auth Microservice URL
   AUTH_SERVICE_URL=http://localhost:3000

   # CORS Configuration
   CORS_ORIGIN=http://localhost:3000
   ```

3. **Database Setup:**
   - Create a MySQL database named `store_microservice`
   - The application will automatically create tables on first run (synchronize: true in development)

4. **Start the application:**
   ```bash
   # Development mode
   npm run start:dev

   # Production mode
   npm run build
   npm run start:prod
   ```

### Authentication

This microservice expects JWT tokens from your auth microservice. The JWT payload should include:

```typescript
{
  sub: string;        // User ID
  email: string;      // User email
  role: 'store';      // User role (must be 'store')
  name: string;       // User name
  phone: string;      // User phone
  address: string;    // User address
  storeData?: {       // Optional store data
    storeName: string;
    storeAddress: string;
    storePhone: string;
  }
}
```

### API Documentation

Once the application is running, visit:
- **Swagger UI**: `http://localhost:3001/api/docs`

### Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## Project Structure

```
src/
├── auth/                 # Authentication module
├── common/              # Shared interfaces and decorators
├── dashboard/           # Dashboard statistics
├── orders/              # Orders management
├── products/            # Products management
├── reviews/             # Reviews management
├── suppliers/           # Suppliers management
├── app.module.ts        # Root module
├── app.controller.ts    # Health check controller
├── app.service.ts       # Basic app service
└── main.ts             # Application entry point
```

## Database Schema

The application uses the following entities:
- **Product**: Store products with pricing, stock, and delivery options
- **Order**: Customer orders with items and status tracking
- **Supplier**: Store suppliers with contact information
- **Review**: Customer reviews and ratings

## Integration with Auth Microservice

This microservice is designed to work with your existing auth microservice:
- Uses the same JWT secret for token validation
- Expects users with role 'store'
- All endpoints require authentication except health check
- User information is extracted from JWT tokens

## Development

```bash
# Watch mode
npm run start:dev

# Debug mode
npm run start:debug

# Lint
npm run lint

# Format
npm run format
```
