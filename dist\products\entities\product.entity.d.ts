import { Document } from 'mongoose';
export interface DeliveryOptions {
    delivery: boolean;
    pickup: boolean;
}
export interface NutritionalInfo {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
}
export declare class Product extends Document {
    name: string;
    description: string;
    price: number;
    stock: number;
    category: string;
    image: string;
    tags: string[];
    deliveryOptions: DeliveryOptions;
    nutritionalInfo?: NutritionalInfo;
    storeId: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare const ProductSchema: import("mongoose").Schema<Product, import("mongoose").Model<Product, any, any, any, Document<unknown, any, Product, any> & Product & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Product, Document<unknown, {}, import("mongoose").FlatRecord<Product>, {}> & import("mongoose").FlatRecord<Product> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
