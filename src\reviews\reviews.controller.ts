import { Controller, Get, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ReviewsService } from './reviews.service';
import { ReviewsDataDto } from './dto/reviews-data.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { GetUser } from '../common/decorators/user.decorator';
import { User } from '../common/interfaces/user.interface';

@ApiTags('Reviews')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('store/reviews')
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all reviews for the store' })
  @ApiResponse({ 
    status: 200, 
    description: 'Reviews retrieved successfully',
    type: ReviewsDataDto,
  })
  findAll(@GetUser() user: User): Promise<ReviewsDataDto> {
    return this.reviewsService.findAll(user);
  }
}
